'use client'

import { useState } from 'react'
import { Plus, X, MapPin, Building2, Home } from 'lucide-react'
import { LocationInput, type LocationSuggestion } from './location-input'
import { Button } from './button'
import { cn } from '@/lib/utils'
import type { LocationType } from '@/types/database'

export interface LocationEntry {
  id?: string
  location_raw: string
  location_normalized?: string
  city?: string
  country?: string
  country_code?: string
  latitude?: number
  longitude?: number
  is_primary: boolean
  is_headquarters: boolean
  location_type: LocationType
}

interface MultiLocationInputProps {
  locations: LocationEntry[]
  onChange: (locations: LocationEntry[]) => void
  maxLocations?: number
  className?: string
  disabled?: boolean
  error?: string
}

const LOCATION_TYPE_OPTIONS: { value: LocationType; label: string; icon: (props: { className?: string }) => JSX.Element }[] = [
  { value: 'headquarters', label: 'Headquarters', icon: (props) => <Building2 className={cn('w-4 h-4', props.className)} /> },
  { value: 'office', label: 'Office', icon: (props) => <Building2 className={cn('w-4 h-4', props.className)} /> },
  { value: 'branch', label: 'Branch', icon: (props) => <Home className={cn('w-4 h-4', props.className)} /> },
  { value: 'remote', label: 'Remote', icon: (props) => <MapPin className={cn('w-4 h-4', props.className)} /> },
]

export function MultiLocationInput({
  locations,
  onChange,
  maxLocations = 10,
  className,
  disabled = false,
  error,
}: MultiLocationInputProps) {
  const [newLocation, setNewLocation] = useState('')

  const handleLocationChange = (index: number, field: keyof LocationEntry, value: unknown) => {
    const updatedLocations = [...locations]
    updatedLocations[index] = { ...updatedLocations[index], [field]: value }
    
    // If setting as primary, unset other primary locations
    if (field === 'is_primary' && value === true) {
      updatedLocations.forEach((loc, i) => {
        if (i !== index) {
          loc.is_primary = false
        }
      })
    }
    
    // If setting as headquarters, unset other headquarters
    if (field === 'is_headquarters' && value === true) {
      updatedLocations.forEach((loc, i) => {
        if (i !== index) {
          loc.is_headquarters = false
        }
      })
    }
    
    onChange(updatedLocations)
  }

  const handleLocationInputChange = (index: number, location: string, suggestion?: LocationSuggestion) => {
    const updatedLocations = [...locations]
    updatedLocations[index] = {
      ...updatedLocations[index],
      location_raw: location,
      location_normalized: suggestion?.normalized || location,
      city: suggestion?.city,
      country: suggestion?.country,
      country_code: suggestion?.countryCode,
      latitude: suggestion?.latitude,
      longitude: suggestion?.longitude,
    }
    onChange(updatedLocations)
  }

  const addLocation = () => {
    if (locations.length >= maxLocations) return
    
    const newLocationEntry: LocationEntry = {
      location_raw: newLocation,
      location_normalized: newLocation,
      is_primary: locations.length === 0, // First location is primary by default
      is_headquarters: locations.length === 0, // First location is headquarters by default
      location_type: locations.length === 0 ? 'headquarters' : 'office',
    }
    
    onChange([...locations, newLocationEntry])
    setNewLocation('')
  }

  const removeLocation = (index: number) => {
    const updatedLocations = locations.filter((_, i) => i !== index)
    
    // If we removed the primary location, make the first remaining location primary
    if (locations[index].is_primary && updatedLocations.length > 0) {
      updatedLocations[0].is_primary = true
    }
    
    // If we removed the headquarters, make the first remaining location headquarters
    if (locations[index].is_headquarters && updatedLocations.length > 0) {
      updatedLocations[0].is_headquarters = true
    }
    
    onChange(updatedLocations)
  }

  const _handleNewLocationSubmit = (location: string, suggestion?: LocationSuggestion) => {
    if (!location.trim()) return
    
    const newLocationEntry: LocationEntry = {
      location_raw: location,
      location_normalized: suggestion?.normalized || location,
      city: suggestion?.city,
      country: suggestion?.country,
      country_code: suggestion?.countryCode,
      latitude: suggestion?.latitude,
      longitude: suggestion?.longitude,
      is_primary: locations.length === 0,
      is_headquarters: locations.length === 0,
      location_type: locations.length === 0 ? 'headquarters' : 'office',
    }
    
    onChange([...locations, newLocationEntry])
    setNewLocation('')
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Existing locations */}
      {locations.map((location, index) => (
        <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Location {index + 1}
              {location.is_primary && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  Primary
                </span>
              )}
              {location.is_headquarters && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                  HQ
                </span>
              )}
            </h4>
            {locations.length > 1 && (
              <button
                type="button"
                onClick={() => removeLocation(index)}
                disabled={disabled}
                className="text-red-500 hover:text-red-700 disabled:opacity-50"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          <LocationInput
            value={location.location_raw}
            onChange={(loc, suggestion) => handleLocationInputChange(index, loc, suggestion)}
            placeholder="Enter location..."
            disabled={disabled}
          />

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {/* Location Type */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                value={location.location_type}
                onChange={(e) => handleLocationChange(index, 'location_type', e.target.value as LocationType)}
                disabled={disabled}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {LOCATION_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Primary checkbox */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={`primary-${index}`}
                checked={location.is_primary}
                onChange={(e) => handleLocationChange(index, 'is_primary', e.target.checked)}
                disabled={disabled}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor={`primary-${index}`} className="text-xs font-medium text-gray-700">
                Primary location
              </label>
            </div>

            {/* Headquarters checkbox */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={`headquarters-${index}`}
                checked={location.is_headquarters}
                onChange={(e) => handleLocationChange(index, 'is_headquarters', e.target.checked)}
                disabled={disabled}
                className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <label htmlFor={`headquarters-${index}`} className="text-xs font-medium text-gray-700">
                Headquarters
              </label>
            </div>
          </div>
        </div>
      ))}

      {/* Add new location */}
      {locations.length < maxLocations && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <LocationInput
                value={newLocation}
                onChange={setNewLocation}
                placeholder="Add another location..."
                disabled={disabled}
                showSuggestions={true}
              />
            </div>
            <Button
              type="button"
              onClick={addLocation}
              disabled={disabled || !newLocation.trim()}
              size="sm"
              className="flex-shrink-0"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {/* Help text */}
      <p className="text-xs text-gray-500">
        Add multiple office locations for your company. The primary location will be displayed prominently.
      </p>
    </div>
  )
}
