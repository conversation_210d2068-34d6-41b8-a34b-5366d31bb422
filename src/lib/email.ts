import nodemailer from 'nodemailer'
import { v4 as uuidv4 } from 'uuid'

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'localhost',
  port: parseInt(process.env.SMTP_PORT || '1025'),
  secure: false, // true for 465, false for other ports
  auth: process.env.NODE_ENV === 'production' ? {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  } : undefined, // No auth needed for MailHog in development
})

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

export async function sendEmail(options: EmailOptions) {
  try {
    const info = await transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    })

    console.log('Email sent:', info.messageId)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Error sending email:', error)
    throw new Error('Failed to send email')
  }
}

export function generateVerificationToken(): string {
  return uuidv4()
}

/**
 * Creates an email notification for users when their reported company is added
 */
export function createCompanyAddedNotificationEmail(
  userEmail: string,
  firstName: string,
  companyName: string,
  companyId: string
): EmailOptions {
  const companyUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/companies/${companyId}`
  const dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Your Company is Now Available - BenefitLens</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9fafb; padding: 20px; }
        .info-box { background-color: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 15px 0; }
        .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 5px; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #6b7280; }
        .highlight { background-color: #dbeafe; padding: 2px 4px; border-radius: 4px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Great News!</h1>
          <p>Your company is now available on BenefitLens</p>
        </div>

        <div class="content">
          <p>Hello ${firstName},</p>

          <div class="info-box">
            <h3>✅ Company Added Successfully</h3>
            <p>We're excited to let you know that <strong>${companyName}</strong> has been added to the BenefitLens platform!</p>
            <p>You can now:</p>
            <ul>
              <li>View and verify benefits for your company</li>
              <li>Connect with other employees</li>
              <li>Access company-specific information</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 20px 0;">
            <a href="${dashboardUrl}" class="button">Go to Dashboard</a>
            <a href="${companyUrl}" class="button" style="background-color: #059669;">View Company Profile</a>
          </div>

          <div class="info-box">
            <h3>What's Next?</h3>
            <p>Visit your dashboard to see your company information and start exploring the benefits available to you.</p>
            <p>If you have any questions or need assistance, feel free to reach out to our support team.</p>
          </div>
        </div>

        <div class="footer">
          <p>This email was sent to ${userEmail}</p>
          <p>BenefitLens - Company Benefits Verification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    BenefitLens - Your Company is Now Available!

    Hello ${firstName},

    Great news! ${companyName} has been added to the BenefitLens platform.

    You can now:
    - View and verify benefits for your company
    - Connect with other employees
    - Access company-specific information

    Visit your dashboard: ${dashboardUrl}
    View company profile: ${companyUrl}

    What's Next?
    Visit your dashboard to see your company information and start exploring the benefits available to you.

    If you have any questions or need assistance, feel free to reach out to our support team.

    Best regards,
    The BenefitLens Team

    This email was sent to ${userEmail}
  `

  return {
    to: userEmail,
    subject: `🎉 ${companyName} is now available on BenefitLens!`,
    html,
    text,
  }
}


