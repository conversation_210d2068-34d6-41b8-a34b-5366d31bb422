// Unified session storage that works in both Node.js and Edge Runtime
import { query } from './local-db'

export interface SessionData {
  userId: string
  expiresAt: string
  createdAt: string
}

// Try to use Redis if available, fallback to PostgreSQL
async function getRedisSession(sessionToken: string): Promise<SessionData | null> {
  try {
    // Only import Redis in Node.js runtime
    if (typeof window === 'undefined' && !process.env.EDGE_RUNTIME) {
      const { getSession } = await import('./redis')
      return await getSession(sessionToken)
    }
  } catch (error) {
    console.warn('Redis not available, falling back to PostgreSQL:', error)
  }
  return null
}

async function setRedisSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  try {
    // Only import Redis in Node.js runtime
    if (typeof window === 'undefined' && !process.env.EDGE_RUNTIME) {
      const { setSession } = await import('./redis')
      return await setSession(sessionToken, userId, expiresAt)
    }
  } catch (error) {
    console.warn('Redis not available for session creation:', error)
  }
  return false
}

async function deleteRedisSession(sessionToken: string): Promise<boolean> {
  try {
    // Only import Redis in Node.js runtime
    if (typeof window === 'undefined' && !process.env.EDGE_RUNTIME) {
      const { deleteSession } = await import('./redis')
      return await deleteSession(sessionToken)
    }
  } catch (error) {
    console.warn('Redis not available for session deletion:', error)
  }
  return false
}

// PostgreSQL fallback functions
async function getPostgreSQLSession(sessionToken: string): Promise<SessionData | null> {
  try {
    const result = await query(
      'SELECT user_id, expires_at, created_at FROM user_sessions WHERE session_token = $1',
      [sessionToken]
    )

    if (result.rows.length === 0) return null

    const session = result.rows[0]

    // Check if session is expired
    if (new Date(session.expires_at) < new Date()) {
      await deletePostgreSQLSession(sessionToken)
      return null
    }

    return {
      userId: session.user_id,
      expiresAt: session.expires_at,
      createdAt: session.created_at,
    }
  } catch (error) {
    console.error('Error getting session from PostgreSQL:', error)
    return null
  }
}

async function setPostgreSQLSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  try {
    await query(
      'INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES ($1, $2, $3)',
      [userId, sessionToken, expiresAt.toISOString()]
    )
    return true
  } catch (error) {
    console.error('Error setting session in PostgreSQL:', error)
    return false
  }
}

async function deletePostgreSQLSession(sessionToken: string): Promise<boolean> {
  try {
    await query('DELETE FROM user_sessions WHERE session_token = $1', [sessionToken])
    return true
  } catch (error) {
    console.error('Error deleting session from PostgreSQL:', error)
    return false
  }
}

// Public API - tries Redis first, falls back to PostgreSQL
export async function getSession(sessionToken: string): Promise<SessionData | null> {
  // Try Redis first
  const redisSession = await getRedisSession(sessionToken)
  if (redisSession) {
    return redisSession
  }

  // Fallback to PostgreSQL
  return await getPostgreSQLSession(sessionToken)
}

export async function setSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  // Try Redis first
  const redisSuccess = await setRedisSession(sessionToken, userId, expiresAt)
  
  // Always also store in PostgreSQL as backup
  const pgSuccess = await setPostgreSQLSession(sessionToken, userId, expiresAt)
  
  return redisSuccess || pgSuccess
}

export async function deleteSession(sessionToken: string): Promise<boolean> {
  // Delete from both Redis and PostgreSQL
  const redisSuccess = await deleteRedisSession(sessionToken)
  const pgSuccess = await deletePostgreSQLSession(sessionToken)
  
  return redisSuccess || pgSuccess
}

export async function deleteAllUserSessions(userId: string): Promise<boolean> {
  try {
    // Delete from PostgreSQL
    await query('DELETE FROM user_sessions WHERE user_id = $1', [userId])
    
    // Try to delete from Redis if available
    try {
      if (typeof window === 'undefined' && !process.env.EDGE_RUNTIME) {
        const { deleteAllUserSessions: deleteRedisUserSessions } = await import('./redis')
        await deleteRedisUserSessions(userId)
      }
    } catch (error) {
      console.warn('Could not delete Redis sessions for user:', error)
    }
    
    return true
  } catch (error) {
    console.error('Error deleting all user sessions:', error)
    return false
  }
}

// Health check function
export async function checkSessionStorageHealth(): Promise<{
  redis: boolean
  postgresql: boolean
}> {
  let redisHealthy = false
  let postgresqlHealthy = false

  // Check Redis
  try {
    if (typeof window === 'undefined' && !process.env.EDGE_RUNTIME) {
      const { checkRedisHealth } = await import('./redis')
      redisHealthy = await checkRedisHealth()
    }
  } catch {
    // Redis not available
  }

  // Check PostgreSQL
  try {
    await query('SELECT 1')
    postgresqlHealthy = true
  } catch {
    // PostgreSQL not available
  }

  return {
    redis: redisHealthy,
    postgresql: postgresqlHealthy
  }
}
