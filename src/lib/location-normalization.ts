import { Country, City } from 'country-state-city'
import type { ICountry, ICity } from 'country-state-city'
import { query } from './local-db'

export interface LocationData {
  raw: string
  normalized: string
  city: string
  country: string
  countryCode: string
  latitude?: number
  longitude?: number
  languageCode?: string
}

// Cache for country data to avoid repeated lookups
let countriesCache: ICountry[] | null = null
let citiesCache: ICity[] | null = null

// Get all countries (cached)
function getAllCountries(): ICountry[] {
  if (!countriesCache) {
    countriesCache = Country.getAllCountries()
  }
  return countriesCache
}

// Get all cities (cached)
function getAllCities(): ICity[] {
  if (!citiesCache) {
    citiesCache = City.getAllCities()
  }
  return citiesCache
}

// Language variant mappings for common cities
const LANGUAGE_VARIANTS: Record<string, string> = {
  // German variants
  'münchen': 'Munich',
  'köln': 'Cologne',
  'düsseldorf': 'Düsseldorf',
  'wien': 'Vienna',
  'zürich': 'Zurich',
  'genf': 'Geneva',
  'basel': 'Basel',
  'bern': 'Bern',

  // Other variants
  'moskau': 'Moscow',
  'warschau': 'Warsaw',
  'prag': 'Prague',

  // Italian city aliases
  'mailand': 'Milan',
  'florenz': 'Florence',
  'venedig': 'Venice',
  'neapel': 'Naples',
  'rom': 'Rome',

  // Reverse mappings for English to local
  'munich': 'Munich',
  'cologne': 'Cologne',
  'vienna': 'Vienna',
  'zurich': 'Zurich',
  'geneva': 'Geneva',
  'moscow': 'Moscow',
  'warsaw': 'Warsaw',
  'prague': 'Prague',
  'milan': 'Milan',
  'florence': 'Florence',
  'venice': 'Venice',
  'naples': 'Naples',
}

// Priority cities that should be ranked higher in search results
const PRIORITY_CITIES: Record<string, number> = {
  // Major German cities (higher priority)
  'Berlin': 100,
  'Munich': 95,
  'Hamburg': 90,
  'Frankfurt am Main': 85,
  'Cologne': 80,
  'Stuttgart': 75,
  'Düsseldorf': 70,
  'Dortmund': 65,
  'Essen': 60,
  'Leipzig': 55,
  'Bremen': 50,
  'Dresden': 45,
  'Hannover': 40,
  'Nuremberg': 35,
  'Duisburg': 30,

  // Other major European cities
  'London': 95,
  'Paris': 95,
  'Amsterdam': 85,
  'Vienna': 80,
  'Zurich': 75,
  'Barcelona': 70,
  'Madrid': 70,
  'Rome': 70,
  'Milan': 65,
  'Florence': 60,
  'Venice': 55,
  'Naples': 50
}

/**
 * Normalizes a location string to a standardized format using comprehensive city database
 * @param location Raw location input
 * @returns Normalized location data
 */
export async function normalizeLocation(location: string): Promise<LocationData> {
  if (!location || location.trim() === '') {
    throw new Error('Location cannot be empty')
  }

  const trimmedLocation = location.trim()

  // Try to find location using comprehensive city database
  const cityMatch = await findCityMatch(trimmedLocation)
  if (cityMatch) {
    return cityMatch
  }

  // If no exact match found, apply intelligent normalization
  return applyIntelligentNormalization(trimmedLocation)
}

/**
 * Find city match using comprehensive city database
 */
async function findCityMatch(location: string): Promise<LocationData | null> {
  const lowerLocation = location.toLowerCase().trim()

  // Handle language variants first
  const normalizedCityName = LANGUAGE_VARIANTS[lowerLocation] || location

  // Parse location input
  const parts = location.split(',').map(p => p.trim())
  const cityPart = parts[0]
  const countryPart = parts[1]

  // Get all cities and countries
  const allCities = getAllCities()
  const allCountries = getAllCountries()

  // Try exact city name match first, then partial matches
  let matchedCities = allCities.filter(city =>
    city.name.toLowerCase() === cityPart.toLowerCase() ||
    city.name.toLowerCase() === normalizedCityName.toLowerCase()
  )

  // If no exact matches, try partial matches for major cities
  if (matchedCities.length === 0) {
    matchedCities = allCities.filter(city =>
      city.name.toLowerCase().includes(cityPart.toLowerCase()) ||
      city.name.toLowerCase().includes(normalizedCityName.toLowerCase())
    )
  }

  // If country is specified, filter by country
  if (countryPart && matchedCities.length > 0) {
    const countryMatch = allCountries.find(country =>
      country.name.toLowerCase() === countryPart.toLowerCase() ||
      country.isoCode.toLowerCase() === countryPart.toLowerCase()
    )

    if (countryMatch) {
      matchedCities = matchedCities.filter(city => city.countryCode === countryMatch.isoCode)
    }
  }

  // If we have matches, return the most relevant one using priority system
  if (matchedCities.length > 0) {
    // Sort by priority (higher priority cities first)
    matchedCities.sort((a, b) => {
      const aPriority = PRIORITY_CITIES[a.name] || 0
      const bPriority = PRIORITY_CITIES[b.name] || 0
      return bPriority - aPriority
    })

    const city = matchedCities[0]
    const country = allCountries.find(c => c.isoCode === city.countryCode)

    if (country) {
      return {
        raw: location,
        normalized: `${city.name}, ${country.name}`,
        city: city.name,
        country: country.name,
        countryCode: country.isoCode,
        latitude: city.latitude ? parseFloat(city.latitude) : undefined,
        longitude: city.longitude ? parseFloat(city.longitude) : undefined,
      }
    }
  }

  // Try partial matches for city names
  if (cityPart.length >= 3) {
    const partialMatches = allCities.filter(city =>
      city.name.toLowerCase().startsWith(cityPart.toLowerCase())
    ).slice(0, 5) // Limit to top 5 matches

    if (partialMatches.length > 0) {
      const city = partialMatches[0]
      const country = allCountries.find(c => c.isoCode === city.countryCode)

      if (country) {
        return {
          raw: location,
          normalized: `${city.name}, ${country.name}`,
          city: city.name,
          country: country.name,
          countryCode: country.isoCode,
          latitude: city.latitude ? parseFloat(city.latitude) : undefined,
          longitude: city.longitude ? parseFloat(city.longitude) : undefined,
        }
      }
    }
  }

  return null
}

/**
 * Applies intelligent normalization when no exact match is found
 */
function applyIntelligentNormalization(location: string): LocationData {
  // Remove extra whitespace and normalize case
  const cleaned = location.replace(/\s+/g, ' ').trim()

  // Check if it already contains a country
  const hasCountry = /,\s*[A-Za-z\s]+$/i.test(cleaned)

  if (hasCountry) {
    // Already has country, just clean up formatting
    const normalized = cleaned.replace(/,\s+/g, ', ')
    const parts = normalized.split(',').map(p => p.trim())
    const city = parts[0]
    const countryName = parts[1] || 'Unknown'

    // Try to find country code
    const allCountries = getAllCountries()
    const country = allCountries.find(c =>
      c.name.toLowerCase() === countryName.toLowerCase() ||
      c.isoCode.toLowerCase() === countryName.toLowerCase()
    )

    return {
      raw: location,
      normalized,
      city,
      country: country?.name || countryName,
      countryCode: country?.isoCode || 'XX',
    }
  }

  // If it looks like a German city (common pattern), add Germany
  if (isLikelyGermanCity(cleaned)) {
    return {
      raw: location,
      normalized: `${cleaned}, Germany`,
      city: cleaned,
      country: 'Germany',
      countryCode: 'DE',
    }
  }

  // Default: assume it's a city and leave country unknown
  return {
    raw: location,
    normalized: cleaned,
    city: cleaned,
    country: 'Unknown',
    countryCode: 'XX',
  }
}

/**
 * Checks if a location is likely a German city based on patterns
 */
function isLikelyGermanCity(location: string): boolean {
  const germanPatterns = [
    /ü|ö|ä|ß/i, // German characters
    /^(Berlin|Munich|Frankfurt|Hamburg|Cologne|Stuttgart|Düsseldorf|Dortmund|Essen|Leipzig|Bremen|Dresden|Hannover|Heidelberg)$/i,
    /(berg|burg|stadt|dorf|heim|hausen|furt|bach)$/i, // Common German city suffixes
  ]
  
  return germanPatterns.some(pattern => pattern.test(location))
}




/**
 * Gets location suggestions for autocomplete using only cities from our database
 */
export async function getLocationSuggestions(q: string, limit: number = 10): Promise<LocationData[]> {
  if (!q || q.trim().length < 2) {
    return []
  }

  const searchQuery = q.trim().toLowerCase()
  const suggestions: LocationData[] = []
  const seenNormalized = new Set<string>()

  // 1) Database-backed suggestions (cities that exist in our data)
  try {
    const result = await query(`
      SELECT DISTINCT city, country, country_code, latitude, longitude,
             COUNT(*) as company_count
      FROM company_locations
      WHERE LOWER(city) LIKE $1 OR LOWER(country) LIKE $1
      GROUP BY city, country, country_code, latitude, longitude
      ORDER BY company_count DESC, city ASC
      LIMIT $2
    `, [`%${searchQuery}%`, limit * 2])

    // Transform DB rows first (more relevant to our data)
    for (const row of result.rows as any[]) {
      const normalized = `${row.city}, ${row.country}`
      if (seenNormalized.has(normalized)) continue
      suggestions.push({
        raw: q,
        normalized,
        city: row.city,
        country: row.country,
        countryCode: row.country_code,
        latitude: row.latitude ? parseFloat(row.latitude) : undefined,
        longitude: row.longitude ? parseFloat(row.longitude) : undefined,
      })
      seenNormalized.add(normalized)
      if (suggestions.length >= limit) break
    }
  } catch (error) {
    console.error('Error getting location suggestions from database:', error)
    // Continue with built-in suggestions below
  }

  if (suggestions.length < limit) {
    // 2) Built-in suggestions using comprehensive city library (fallback)
    const allCities = getAllCities()
    const allCountries = getAllCountries()

    // Gather matches with a simple scoring
    const builtInMatches = allCities
      .map((city) => {
        const name = city.name
        const lower = name.toLowerCase()
        let score = 0
        if (lower === searchQuery) score = 1000
        else if (lower.startsWith(searchQuery)) score = 500
        else if (lower.includes(searchQuery)) score = 100
        // Add priority boost for well-known cities
        score += (PRIORITY_CITIES[name] || 0)
        return { city, score }
      })
      .filter((m) => m.score > 0)
      .sort((a, b) => b.score - a.score)

    for (const match of builtInMatches) {
      if (suggestions.length >= limit) break
      const ctry = allCountries.find((c) => c.isoCode === match.city.countryCode)
      if (!ctry) continue
      const normalized = `${match.city.name}, ${ctry.name}`
      if (seenNormalized.has(normalized)) continue

      suggestions.push({
        raw: q,
        normalized,
        city: match.city.name,
        country: ctry.name,
        countryCode: ctry.isoCode,
        latitude: match.city.latitude ? parseFloat(match.city.latitude) : undefined,
        longitude: match.city.longitude ? parseFloat(match.city.longitude) : undefined,
      })
      seenNormalized.add(normalized)
    }
  }

  return suggestions.slice(0, limit)
}
