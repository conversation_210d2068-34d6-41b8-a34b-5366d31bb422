import { createClient } from 'redis'

// Create Redis client
const redis = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 5000,
  },
})

// Error handling
redis.on('error', (err) => {
  console.error('Redis Client Error:', err)
})

redis.on('connect', () => {
  console.log('Redis Client Connected')
})

redis.on('ready', () => {
  console.log('Redis Client Ready')
})

redis.on('end', () => {
  console.log('Redis Client Disconnected')
})

// Connect to Redis
let isConnecting = false
let isConnected = false

export async function connectRedis() {
  if (isConnected || isConnecting) {
    return redis
  }

  try {
    isConnecting = true
    await redis.connect()
    isConnected = true
    isConnecting = false
    return redis
  } catch (error) {
    isConnecting = false
    console.error('Failed to connect to Redis:', error)
    throw error
  }
}

// Graceful shutdown - only register signal handlers once to prevent memory leaks
if (!process.env.REDIS_SIGNAL_HANDLERS_REGISTERED) {
  process.env.REDIS_SIGNAL_HANDLERS_REGISTERED = 'true'
  process.on('SIGINT', async () => {
    if (isConnected) {
      await redis.quit()
    }
  })

  process.on('SIGTERM', async () => {
    if (isConnected) {
      await redis.quit()
    }
  })
}

// Session management functions
export async function setSession(sessionToken: string, userId: string, expiresAt: Date) {
  try {
    await connectRedis()
    const sessionData = {
      userId,
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString(),
    }
    
    // Set session with expiration
    const ttlSeconds = Math.floor((expiresAt.getTime() - Date.now()) / 1000)
    await redis.setEx(`session:${sessionToken}`, ttlSeconds, JSON.stringify(sessionData))
    
    return true
  } catch (error) {
    console.error('Error setting session in Redis:', error)
    return false
  }
}

export async function getSession(sessionToken: string) {
  try {
    await connectRedis()
    const sessionData = await redis.get(`session:${sessionToken}`)
    
    if (!sessionData) {
      return null
    }
    
    const session = JSON.parse(sessionData)
    
    // Check if session is expired
    if (new Date(session.expiresAt) < new Date()) {
      await deleteSession(sessionToken)
      return null
    }
    
    return session
  } catch (error) {
    console.error('Error getting session from Redis:', error)
    return null
  }
}

export async function deleteSession(sessionToken: string) {
  try {
    await connectRedis()
    await redis.del(`session:${sessionToken}`)
    return true
  } catch (error) {
    console.error('Error deleting session from Redis:', error)
    return false
  }
}

export async function deleteAllUserSessions(userId: string) {
  try {
    await connectRedis()
    const keys = await redis.keys('session:*')
    
    for (const key of keys) {
      const sessionData = await redis.get(key)
      if (sessionData) {
        const session = JSON.parse(sessionData)
        if (session.userId === userId) {
          await redis.del(key)
        }
      }
    }
    
    return true
  } catch (error) {
    console.error('Error deleting user sessions from Redis:', error)
    return false
  }
}

// Caching functions
export async function setCache(key: string, value: any, ttlSeconds: number = 3600) {
  try {
    await connectRedis()
    await redis.setEx(`cache:${key}`, ttlSeconds, JSON.stringify(value))
    return true
  } catch (error) {
    console.error('Error setting cache in Redis:', error)
    return false
  }
}

export async function getCache(key: string) {
  try {
    await connectRedis()
    const cachedData = await redis.get(`cache:${key}`)
    
    if (!cachedData) {
      return null
    }
    
    return JSON.parse(cachedData)
  } catch (error) {
    console.error('Error getting cache from Redis:', error)
    return null
  }
}

export async function deleteCache(key: string) {
  try {
    await connectRedis()
    await redis.del(`cache:${key}`)
    return true
  } catch (error) {
    console.error('Error deleting cache from Redis:', error)
    return false
  }
}

export async function clearCachePattern(pattern: string) {
  try {
    await connectRedis()
    const keys = await redis.keys(`cache:${pattern}`)
    
    if (keys.length > 0) {
      await redis.del(keys)
    }
    
    return true
  } catch (error) {
    console.error('Error clearing cache pattern from Redis:', error)
    return false
  }
}

// Health check
export async function checkRedisHealth() {
  try {
    await connectRedis()
    const result = await redis.ping()
    return result === 'PONG'
  } catch (error) {
    console.error('Redis health check failed:', error)
    return false
  }
}

export { redis }
