# BenefitLens - Company Benefits Comparison Platform

A comprehensive web application for comparing companies based on their employee benefits. Users can search and filter companies by benefits, location, and other criteria, while company representatives can verify and manage their benefit listings.

## Features

- 🔍 **Advanced Search & Filtering**: Find companies by benefits, location, size, and industry
- 🏢 **Company Profiles**: Detailed company information with verified benefit listings
- ✅ **Verification System**: Company email domain verification and benefit confirmation
- 👥 **Admin Dashboard**: Company representatives can claim and manage profiles
- 📱 **Responsive Design**: Optimized for mobile and desktop
- 🔐 **Authentication**: Secure magic link authentication with session management
- 🗄️ **PostgreSQL Database**: Self-hosted PostgreSQL with Redis caching

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL
- **Authentication**: Magic link authentication with Redis session storage
- **UI Components**: Radix <PERSON>, Lucide React
- **Deployment**: Self-hosted (Kubernetes)

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Docker and Docker Compose (for local development)
- PostgreSQL database
- Redis (for session storage and caching)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd benefitlens
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Fill in your environment variables:
- PostgreSQL database connection
- Redis connection
- App URL
- SMTP settings for magic link emails

4. Start the development environment:
```bash
npm run dev:start
```

This will start PostgreSQL, Redis, and MailHog containers, then run the development server.

5. Alternatively, run services separately:
```bash
# Start database and Redis
docker-compose up -d

# Run the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

The database will be automatically initialized with schema and sample data on first run.

## Database Schema

The application uses the following main tables:
- `companies`: Company information and verification status
- `benefits`: Available benefits with categories
- `company_benefits`: Junction table linking companies to benefits
- `company_users`: Company representatives and verification
- `benefit_verifications`: User verification of company benefits
- `job_postings`: Future job posting functionality

## Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # Reusable UI components
├── lib/                # Utility functions and configurations
├── types/              # TypeScript type definitions
└── styles/             # Global styles

database/
├── schema.sql          # Database schema
└── seed.sql           # Sample data
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Current Status

✅ **MVP Complete** - The application includes all core features:

- **Search & Filtering**: Advanced company search by benefits, location, size, and industry
- **Company Profiles**: Detailed company pages with benefit listings
- **Authentication**: Session-based user authentication with email verification
- **Company Verification**: Email domain-based company claiming system
- **Benefit Verification**: Community-driven benefit confirmation/dispute system
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS
- **Database**: PostgreSQL schema with proper relationships and RLS policies

## Quick Start

### Local Development (Recommended)

For local development with Docker containers (no external services needed):

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd workwell
   ./scripts/dev-setup.sh
   ```

2. **Start development**:
   ```bash
   npm run dev
   ```

3. **Access the application**:
   - App: http://localhost:3000
   - Database UI: http://localhost:8080
   - Email testing: http://localhost:8025

**Test accounts** (password: `password123`):
- `<EMAIL>` (SAP employee)
- `<EMAIL>` (Deutsche Bank employee)
- `<EMAIL>` (general user)

See [LOCAL_DEVELOPMENT.md](./LOCAL_DEVELOPMENT.md) for detailed setup instructions.

### Production Setup

For production deployment in Kubernetes:

1. **Clone and install**:
   ```bash
   git clone <repository-url>
   cd workwell
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Fill in your database and Redis credentials
   ```

3. **Set up database**:
   - Deploy PostgreSQL in your cluster or use external instance
   - Run `database/schema.sql`
   - Optionally run `database/seed.sql`

4. **Deploy to Kubernetes**:
   ```bash
   # Build and push Docker image
   docker build -t your-registry/workwell:latest .
   docker push your-registry/workwell:latest

   # Apply Kubernetes manifests
   kubectl apply -f k8s/
   ```

## Deployment

### Local Development
See [LOCAL_DEVELOPMENT.md](./docs/LOCAL_DEVELOPMENT.md) for local setup instructions.

### Kubernetes Production
See [DEPLOYMENT.md](./docs/DEPLOYMENT.md) for Kubernetes deployment instructions.

### Quick Kubernetes Deployment
```bash
# Set your Docker registry
export DOCKER_REGISTRY=your-registry.com

# Deploy everything
./scripts/deploy-k8s.sh

# Initialize database
./scripts/init-db-k8s.sh
```

## Testing

See [TESTING.md](./TESTING.md) for comprehensive testing guide.

## License

This project is licensed under the MIT License.
