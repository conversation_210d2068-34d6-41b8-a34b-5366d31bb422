#!/bin/bash

# BenefitLens Local Development Setup Script

echo "🚀 Setting up BenefitLens for local development..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker is running"

# Start the services
echo "🐳 Starting Docker services..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d
else
    docker compose up -d
fi

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is ready
until docker exec benefitlens-postgres pg_isready -U benefitlens_user -d benefitlens > /dev/null 2>&1; do
    echo "⏳ PostgreSQL is not ready yet, waiting..."
    sleep 2
done

echo "✅ PostgreSQL is ready"

# Check if Redis is ready
echo "⏳ Checking Redis..."
until docker exec benefitlens-redis redis-cli ping > /dev/null 2>&1; do
    echo "⏳ Redis is not ready yet, waiting..."
    sleep 2
done

echo "✅ Redis is ready"

# Install Node.js dependencies if not already installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

echo "🎉 Local development environment is ready!"
echo ""
echo "📋 Services running:"
echo "   🐘 PostgreSQL: localhost:5432"
echo "   🔴 Redis: localhost:6379"
echo "   📧 MailHog: http://localhost:8025"
echo "   🗄️  Adminer: http://localhost:8080"
echo ""
echo "🔑 Database credentials:"
echo "   Database: benefitlens"
echo "   Username: benefitlens_user"
echo "   Password: benefitlens_password"
echo ""
echo "👥 Test user accounts (password: password123):"
echo "   <EMAIL> (SAP employee)"
echo "   <EMAIL> (Deutsche Bank employee)"
echo "   <EMAIL> (Accenture employee)"
echo "   <EMAIL> (General user)"
echo ""
echo "🚀 To start the development server:"
echo "   npm run dev"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
